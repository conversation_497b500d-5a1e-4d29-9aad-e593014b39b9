import { getCookie } from "@/actions/cookies";
import PatientChatViewPage from "@/components/chat/patient-chat-view";
import PatientOtpLogin from "@/components/chat/patient-otp-login";
import ErrorRendered from "@/components/shared/error/error-renderer";
import { apiServer } from "@/lib/apiServer";
import CONSTANT from "@/lib/constant";
import React from "react";
export const dynamic = "force-dynamic";
export const fetchCache = "force-no-store";

interface Props {
  params: {
    roomId: string;
  };
}

const UserChatPage = async ({ params }: Props) => {
  const roomId = params.roomId;
  const token = await getCookie(CONSTANT.ACCESS_TOKEN);
  const res = await apiServer({
    method: "GET",
    endpoint: `chat/${roomId}/metadata`,
    data: {},
    token: token || undefined,
  });

  if (res.statusCode === 401) {
    return (
      <>
        <PatientOtpLogin roomIdentifier={roomId} />
      </>
    );
  }

  if (res.statusCode >= 400) {
    return <ErrorRendered statusCode={res.statusCode} />;
  }

  const patient = res?.data?.patient;
  const provider = res?.data?.provider;
  const room = res?.data?.room;
  return (
    <>
      {/* <ChatViewPage patient={patient} provider={provider} messages={messages} room={room} role='USER' />
       */}

      <PatientChatViewPage
        roomIdentifier={roomId}
        role={"USER"}
        patient={patient}
        provider={provider}
        room={room}
      />
    </>
  );
};

export default UserChatPage;
