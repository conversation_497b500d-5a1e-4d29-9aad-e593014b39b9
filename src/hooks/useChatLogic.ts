"use client";
import { useContext, useEffect, useCallback } from "react";
import { SocketContext } from "@/context/socket";
import { useChatContext } from "@/context/chat";
import useNotification from "@/hooks/useNotification";

interface User {
  user_id: string | number;
  first_name: string;
  last_name: string;
}

interface UseChatLogicProps {
  roomId: string;
  roomIdentifier: string;
  patient: User;
  provider: User;
  role: string;
  token?: string;
}

export const useChatLogic = ({
  roomId,
  roomIdentifier,
  patient,
  provider,
  role,
  token,
}: UseChatLogicProps) => {
  // Calculate derived state that was previously in components
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;
  const currentUser = role === "USER" ? patient : provider;
  const { socket, isConnected } = useContext(SocketContext);
  const {
    messageHistory,
    setMessageHistory,
    getMessageHistory,
    readAllMessage,
    handledMessageSend,
    uploadImage,
    getFileSignUrl,
    toggleSwitch,
    setIsConsultOpen,
    setShowPopup,
  } = useChatContext();
  const sendNotification = useNotification();

  // Initialize chat room and load message history
  useEffect(() => {
    if (roomIdentifier) {
      getMessageHistory(roomIdentifier, token);
      readAllMessage(roomIdentifier, token);
    }
  }, [roomIdentifier, getMessageHistory, readAllMessage, token]);

  // Join socket room when connected
  useEffect(() => {
    if (socket && currentUserId && roomId) {
      socket.emit("join", {
        room_id: roomId,
        user_id: currentUserId,
        name: senderFullName,
      });
    }
  }, [socket, currentUserId, roomId, senderFullName]);

  // Handle incoming messages
  useEffect(() => {
    if (!socket) return;

    const handleReceiveMessage = (data: any) => {
      const newMessage = [...(messageHistory ?? []), data];

      // Get sender name from the received message data
      const senderName =
        data.sender?.first_name && data.sender?.last_name
          ? `${data.sender.first_name} ${data.sender.last_name}`
          : data.sender?.name || "Unknown User";

      sendNotification({
        title: `New Message from ${senderName}`,
        body: data.message,
      });

      setMessageHistory(newMessage);
      readAllMessage(roomIdentifier, token);
    };

    const handleToggleRoomStatus = (data: any) => {
      const { room_status } = data;
      if (room_status === "inactive") {
        console.log("Chat room closed by provider");
        setIsConsultOpen(false);
        setShowPopup(true);
      } else {
        setIsConsultOpen(true);
        setShowPopup(false);
      }
    };

    socket.on("receive-message", handleReceiveMessage);
    socket.on("toggle-room-status", handleToggleRoomStatus);

    return () => {
      socket.off("receive-message", handleReceiveMessage);
      socket.off("toggle-room-status", handleToggleRoomStatus);
    };
  }, [
    socket,
    messageHistory,
    sendNotification,
    setMessageHistory,
    readAllMessage,
    roomIdentifier,
    token,
    setIsConsultOpen,
    setShowPopup,
  ]);

  // Wrapper functions for easier component usage
  const handleMessageSend = useCallback(
    (message: string) => {
      handledMessageSend(message, roomId, currentUser);
    },
    [handledMessageSend, roomId, currentUser],
  );

  const handleFileUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      uploadImage(e, roomId, currentUser);
    },
    [uploadImage, roomId, currentUser],
  );

  const handleOnAttached = useCallback(() => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = role === "USER" ? "image/*,.pdf,.doc,.docx" : "image/*";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileUpload({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  }, [handleFileUpload, role]);

  const handleToggleSwitch = useCallback(
    (enabled: boolean, patientId: string | number) => {
      toggleSwitch(
        enabled,
        roomId,
        currentUserId,
        patientId,
        senderFullName,
        token,
      );
    },
    [toggleSwitch, roomId, currentUserId, senderFullName, token],
  );

  const handleFileClick = useCallback(
    (filePath: string) => {
      getFileSignUrl(filePath);
    },
    [getFileSignUrl],
  );

  return {
    // Socket state
    isConnected,

    // Calculated state (moved from components)
    providerFullName,
    patientFullName,
    receiverFullName,
    senderFullName,
    currentUserId,
    currentUser,

    // Message handlers
    handleMessageSend,
    handleOnAttached,
    handleFileClick,

    // Provider-specific handlers
    handleToggleSwitch,

    // Utility functions
    refreshMessageHistory: () => getMessageHistory(roomIdentifier, token),
    markAllAsRead: () => readAllMessage(roomIdentifier, token),
  };
};

export default useChatLogic;
