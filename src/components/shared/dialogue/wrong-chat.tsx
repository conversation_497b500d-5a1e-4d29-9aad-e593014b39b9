import {
  AlertDialogContent,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { AlertDialog } from "@radix-ui/react-alert-dialog";

export const WrongChat = ({ open }: { open: boolean }) => {
  return (
    <AlertDialog open={open}>
      <AlertDialogContent>
        <AlertDialogTitle>Wrong Chat Link!!!</AlertDialogTitle>
        You open a wrong chat. Please check the link and open correct Link.
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default WrongChat;
