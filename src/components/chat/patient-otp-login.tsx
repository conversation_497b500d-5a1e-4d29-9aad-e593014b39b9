"use client";

import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON>, buttonVariants } from "@/components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { apiClient } from "@/lib/api";
import { toast } from "sonner";
import { LoaderCircleIcon, RotateCcw } from "lucide-react";
import { AxiosError } from "axios";
import { User } from "@/types/chat";
import { Loader } from "../shared/loader";
export default function PatientOtpLogin({
  roomIdentifier,
}: {
  roomIdentifier: string;
}) {
  const [otpModalVisible, setOtpModalVisible] = useState(false);
  const [disableResendOtp, setDisableResendOtp] = useState(true);
  const [otpModalValue, setOtpModalValue] = useState("");
  const [user, setUser] = useState<User>();
  const [loader, setLoader] = useState(true);
  const [resendButtonLoading, setResendButtonLoading] = useState(false);
  const [submitButtonLoading, setSubmitButtonLoading] = useState(false);

  const checkLogin = async () => {
    // Use cookie-based token management instead of localStorage
    const { getAuthToken } = await import("@/lib/tokenManager");
    const token = await getAuthToken();
    if (!token) {
      sendOtp();
    }
  };

  const sendOtp = async () => {
    try {
      setResendButtonLoading(true);
      setDisableResendOtp(true);
      const res = await apiClient({
        method: "POST",
        endpoint: "auth/send-otp",
        data: { room_identifier: roomIdentifier },
      });

      toast.success(res.message);
      setDisableResendOtp(false);
      setOtpModalVisible(true);
      setUser(res.data.user);
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 429) {
          toast.error("Too many requests. Please try again later.");
        } else {
          toast.error(error.response?.data?.message || "An error occurred");
        }
      }
      setDisableResendOtp(false);
    } finally {
      setLoader(false);
      setResendButtonLoading(false);
    }
  };

  const verifyUserOtp = async () => {
    if (!otpModalValue) {
      toast.error("Please enter otp");
      return;
    }
    setSubmitButtonLoading(true);
    try {
      const res = await apiClient({
        method: "POST",
        endpoint: "auth/verify-otp",
        data: { user_id: user?.user_guid, otp: otpModalValue },
      });
      setOtpModalVisible(false);
      // Use cookie-based token management instead of localStorage
      const { setAuthToken } = await import("@/lib/tokenManager");
      await setAuthToken(res.data.token, res.data.user);
      toast.success(res.message);

      window.location.reload();
    } catch (error) {
      if (error instanceof AxiosError) {
        toast.error(error.response?.data?.message || "An error occurred");
      } else {
        toast.error("An error occurred");
      }
    } finally {
      setSubmitButtonLoading(false);
    }
  };

  useEffect(() => {
    checkLogin();
  }, []);
  return (
    <>
      <Loader show={loader} />
      <Dialog open={otpModalVisible}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Enter OTP</DialogTitle>
            <DialogDescription>
              Enter the OTP sent to your email.
            </DialogDescription>
          </DialogHeader>
          <div className="flex flex-col gap-4 py-4 justify-center items-center">
            <InputOTP
              maxLength={6}
              value={otpModalValue}
              pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
              onChange={(value) => setOtpModalValue(value)}
            >
              <InputOTPGroup>
                <InputOTPSlot index={0} />
                <InputOTPSlot index={1} />
                <InputOTPSlot index={2} />
              </InputOTPGroup>
              <InputOTPSeparator />
              <InputOTPGroup>
                <InputOTPSlot index={3} />
                <InputOTPSlot index={4} />
                <InputOTPSlot index={5} />
              </InputOTPGroup>
            </InputOTP>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              disabled={disableResendOtp}
              onClick={() => sendOtp()}
            >
              {resendButtonLoading ? (
                <LoaderCircleIcon className="h-4 w-4 animate-spin" />
              ) : (
                <>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Resend OTP
                </>
              )}
            </Button>
            <Button
              variant="default"
              type="submit"
              onClick={() => verifyUserOtp()}
            >
              {submitButtonLoading ? (
                <LoaderCircleIcon className="h-4 w-4 animate-spin" />
              ) : (
                "Submit"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
