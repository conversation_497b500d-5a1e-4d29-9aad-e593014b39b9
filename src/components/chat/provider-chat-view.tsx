"use client";
import {
  <PERSON><PERSON>,
  <PERSON>t<PERSON>ontainer,
  Conversation<PERSON>eader,
  Message,
  MessageInput,
  MessageList,
} from "@chatscope/chat-ui-kit-react";
import { CheckCircle } from "lucide-react";
import React from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { Dialog, DialogContent } from "../ui/dialog";
import FileDisplayContainer from "@/components/fileDisplayContainer";
import { Loader } from "../shared/loader";
import { isImageType, formatChatMessageDate } from "@/lib/utils";

import LogoutButton from "@/components/auth/logout-button";
import { Switch } from "../ui/switch";
import ParticipantsPopup from "./participants-popup";
import { Users } from "lucide-react";
import { Button } from "../ui/button";
import { useChatContext } from "@/context/chat";
import useChatLogic from "@/hooks/useChatLogic";
interface User {
  user_id: string;
  first_name: string;
  last_name: string;
  messageHistory: any[];
}

interface Props {
  patient: User;
  provider: User;
  room: any;
  role: "USER" | "PROVIDER";
  token: string;
  roomIdentifier: string;
}

export default function ProviderChatViewPage({
  patient,
  provider,
  room,
  role,
  token,
  roomIdentifier,
}: Props) {
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const roomId = room?.id;
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;
  const currentUser = role === "USER" ? patient : provider;

  // Use chat context for state management
  const {
    messageHistory,
    loader,
    imageModalVisible,
    imageModalValue,
    chatMetadata,
    participantsPopupVisible,
    setParticipantsPopupVisible,
    setImageModalVisible,
  } = useChatContext();

  // Use chat logic hook for handlers and socket management
  const {
    isConnected,
    handleMessageSend,
    handleOnAttached,
    handleFileClick,
    handleToggleSwitch,
  } = useChatLogic({
    roomId,
    roomIdentifier,
    currentUserId,
    currentUser,
    role,
    receiverFullName,
    senderFullName,
    token,
  });

  return (
    <>
      <Loader show={loader} />
      <Dialog
        open={imageModalVisible}
        onOpenChange={() => setImageModalVisible(false)}
      >
        <DialogContent className="sm:max-w-[925px]">
          <FileDisplayContainer
            src={imageModalValue}
            imageModalVisible={imageModalVisible}
            setImageModalVisible={setImageModalVisible}
          />
        </DialogContent>
      </Dialog>
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar
            name={receiverFullName}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content
            userName={receiverFullName}
            info={
              <div className="text-xs text-gray-500 space-y-1">
                {chatMetadata?.room?.description && (
                  <div>{chatMetadata.room.description}</div>
                )}
                {chatMetadata?.service?.display_service_name && (
                  <div className="font-medium text-blue-600">
                    Service: {chatMetadata.service.display_service_name}
                  </div>
                )}
              </div>
            }
          />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {chatMetadata?.participants && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setParticipantsPopupVisible(true)}
                title="View Participants"
              >
                <Users className="h-4 w-4" />
              </Button>
            )}
            <Switch
              id="chat-switch"
              defaultChecked={room.active}
              onCheckedChange={(e) => {
                handleToggleSwitch(e, patient?.user_id);
              }}
            />
            {isConnected && <CheckCircle />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              const isSendedByMe = data.sender.user_id === currentUserId;
              const fullName =
                data.sender?.first_name && data.sender?.last_name
                  ? `${data.sender.first_name} ${data.sender.last_name}`
                  : data.sender?.name || "Unknown";
              const formattedTime = formatChatMessageDate(data.created_at);
              return (
                <Message
                  key={idx}
                  onClick={() => {
                    if (data.file?.path) {
                      handleFileClick(data.file.path);
                    }
                  }}
                  model={{
                    direction: isSendedByMe ? "outgoing" : "incoming",
                    // message: data?.message?.replace(/<[^>]*>?/gm, ''),
                    position: "last",
                    sender: senderFullName,
                    sentTime: data?.created_at,
                    type: "html",
                  }}
                >
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path || "")
                          ? "file.png"
                          : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent
                      html={`<b>${fullName}: </b> <br/>${data?.message} <br/><em style='font-size:10px'>${formattedTime}</em>`}
                    />
                  )}
                  {/* {data?.message === "ATTACHMENT" ? <Message.ImageContent className='cursor-pointer' src={`/images/file.png`} height={100} /> : <Message.HtmlContent html={data?.message} />} */}
                  {isSendedByMe ? (
                    <></>
                  ) : (
                    <Avatar
                      name="Sumanta Dey"
                      src={`/images/${
                        role === "PROVIDER" ? "patient.png" : "provider.png"
                      }`}
                    />
                  )}
                </Message>
              );
            })}
        </MessageList>
        <MessageInput
          autoFocus
          placeholder="Type message here"
          onSend={handleMessageSend}
          onAttachClick={handleOnAttached}
        />
      </ChatContainer>

      {/* Participants Popup */}
      {chatMetadata?.participants && (
        <ParticipantsPopup
          isOpen={participantsPopupVisible}
          onClose={() => setParticipantsPopupVisible(false)}
          participants={chatMetadata.participants}
        />
      )}
    </>
  );
}
