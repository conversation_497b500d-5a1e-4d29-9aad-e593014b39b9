"use client";
import React, { Suspense } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { CheckCircle, Users } from "lucide-react";
import {
  Chat<PERSON>ontainer,
  ConversationHeader,
  Message,
  MessageList,
  Avatar,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";

import { isImageType, formatChatMessageDate } from "@/lib/utils";
import FileDisplayContainer from "../fileDisplayContainer";
import { Loader } from "../shared/loader";
import LogoutButton from "@/components/auth/logout-button";
import WrongChat from "../shared/dialogue/wrong-chat";
import { ClosedSession } from "../shared/dialogue/closed-session";
import { Button } from "../ui/button";
import { useChatContext } from "@/context/chat";
import useChatLogic from "@/hooks/useChatLogic";
import ParticipantsPopup from "./participants-popup";

type User = {
  email: string;
  first_name: string;
  last_name: string;
  status: string;
  user_guid: string;
  user_id: number;
};

export default function PatientChatViewPage({
  roomIdentifier,
  role,
  patient,
  provider,
  room,
}: {
  roomIdentifier: string;
  role: string;
  patient: User;
  provider: User;
  room: any;
}) {
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const roomId = room?.id;
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;
  const currentUser = role === "USER" ? patient : provider;

  // Use chat context for state management
  const {
    messageHistory,
    loader,
    imageModalVisible,
    imageModalValue,
    chatMetadata,
    participantsPopupVisible,
    setParticipantsPopupVisible,
    isConsultOpen,
    showPopup,
    wrongChat,
    setImageModalVisible,
    setShowPopup,
  } = useChatContext();

  // Use chat logic hook for handlers and socket management
  const { isConnected, handleMessageSend, handleOnAttached, handleFileClick } =
    useChatLogic({
      roomId,
      roomIdentifier,
      currentUserId,
      currentUser,
      role,
      receiverFullName,
      senderFullName,
    });

  return (
    <Suspense>
      <Loader show={loader} />
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar
            name={"receiverFullName"}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content
            userName={receiverFullName}
            info={
              <div className="text-xs text-gray-500 space-y-1">
                {chatMetadata?.room?.description && (
                  <div>{chatMetadata.room.description}</div>
                )}
                {chatMetadata?.service?.display_service_name && (
                  <div className="font-medium text-blue-600">
                    Service: {chatMetadata.service.display_service_name}
                  </div>
                )}
              </div>
            }
          />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {chatMetadata?.participants && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setParticipantsPopupVisible(true)}
                title="View Participants"
              >
                <Users className="h-4 w-4" />
              </Button>
            )}

            {isConnected && <CheckCircle />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              const isSendedByMe = data.sender.user_id === currentUserId;
              const fullName =
                data.sender?.first_name && data.sender?.last_name
                  ? `${data.sender.first_name} ${data.sender.last_name}`
                  : data.sender?.name || "Unknown";
              const formattedTime = formatChatMessageDate(data.created_at);
              return (
                <Message
                  key={idx}
                  onClick={() => {
                    if (data.file?.path) {
                      handleFileClick(data.file.path);
                    }
                  }}
                  model={{
                    direction: isSendedByMe ? "outgoing" : "incoming",
                    // message: data?.message?.replace(/<[^>]*>?/gm, ''),
                    position: "last",
                    sender: senderFullName,
                    sentTime: data?.created_at,
                    type: "html",
                  }}
                >
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path || "")
                          ? "file.png"
                          : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent
                      html={`<b>${fullName}: </b> <br/>${data?.message} <br/><em style='font-size:10px'>${formattedTime}</em>`}
                    />
                  )}
                  {/* {data?.message === "ATTACHMENT" ? <Message.ImageContent className='cursor-pointer' src={`/images/file.png`} height={100} /> : <Message.HtmlContent html={data?.message} />} */}
                  {isSendedByMe ? (
                    <></>
                  ) : (
                    <Avatar
                      name="Sumanta Dey"
                      src={`/images/${
                        role === "PROVIDER" ? "patient.png" : "provider.png"
                      }`}
                    />
                  )}
                </Message>
              );
            })}
        </MessageList>
        {isConsultOpen && (
          <MessageInput
            autoFocus
            placeholder="Type message here"
            onSend={handleMessageSend}
            onAttachClick={handleOnAttached}
          />
        )}
      </ChatContainer>

      <FileDisplayContainer
        src={imageModalValue}
        imageModalVisible={imageModalVisible}
        setImageModalVisible={setImageModalVisible}
      />
      {showPopup && (
        <ClosedSession open={showPopup} onOpenChange={setShowPopup} />
      )}

      <WrongChat open={wrongChat} />
      {/* Participants Popup */}
      {chatMetadata?.participants && (
        <ParticipantsPopup
          isOpen={participantsPopupVisible}
          onClose={() => setParticipantsPopupVisible(false)}
          participants={chatMetadata.participants}
        />
      )}
    </Suspense>
  );
}
