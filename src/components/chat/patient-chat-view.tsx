"use client";
import React, { Suspense, useContext, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { CheckCircle, RotateCcw, Users } from "lucide-react";
import {
  Cha<PERSON><PERSON>ontainer,
  ConversationHeader,
  Message,
  MessageList,
  Avatar,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";

import { apiClient } from "@/lib/api";
import { toast } from "sonner";

import { validateFile, isImageType, formatChatMessageDate } from "@/lib/utils";
import FileDisplayContainer from "../fileDisplayContainer";
import { Loader } from "../shared/loader";
import { SocketContext } from "@/context/socket";
import useNotification from "@/hooks/useNotification";
import { ChatMetadata } from "@/types/chat";
import ParticipantsPopup from "./participants-popup";

import LogoutButton from "@/components/auth/logout-button";
import { useRouter } from "next/navigation";
import WrongChat from "../shared/dialogue/wrong-chat";
import { ClosedSession } from "../shared/dialogue/closed-session";
import { Button } from "../ui/button";

type User = {
  email: string;
  first_name: string;
  last_name: string;
  status: string;
  user_guid: string;
  user_id: number;
};

export default function PatientChatViewPage({
  roomIdentifier,
  role,
  patient,
  provider,
  room,
}: {
  roomIdentifier: string;
  role: string;
  patient: User;
  provider: User;
  room: any;
}) {
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const [messageHistory, setMessageHistory] = useState<any[]>([]);
  const [isConsultOpen, setIsConsultOpen] = useState<boolean>(true);
  const [showPopup, setShowPopup] = useState<boolean>();
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const [chatMetadata, setChatMetadata] = useState<ChatMetadata | null>(null);
  const [participantsPopupVisible, setParticipantsPopupVisible] =
    useState(false);
  const roomId = room?.id;
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;
  const [loader, setLoader] = useState(false);
  const [user, setUser] = useState<User>(patient);
  const { socket, isConnected } = useContext(SocketContext);

  const [wrongChat, setWrongChat] = useState(false);
  const router = useRouter();
  const sendNotification = useNotification();

  const readAllMessage = async () => {
    // Use cookie-based token management instead of localStorage
    const { getAuthToken } = await import("@/lib/tokenManager");
    const token = await getAuthToken();
    await apiClient({
      method: "GET",
      endpoint: `chat/${roomIdentifier}/read-all-messages`,
      data: {},
      token: token || undefined,
    });
  };

  const getMessageHistory = async (roomIdentifier: string) => {
    setLoader(true);
    try {
      // Use cookie-based token management instead of localStorage
      const { getAuthToken } = await import("@/lib/tokenManager");
      const token = await getAuthToken();

      const res = await apiClient({
        method: "GET",
        endpoint: `chat/${roomIdentifier}/messages`,
        data: {},
        token: token || undefined,
      });

      setMessageHistory(res.data.messages);
      setUser(patient);

      if (res.data.chatMetadata?.room?.status === "active") {
        setIsConsultOpen(true);
        setShowPopup(false);
      } else {
        setIsConsultOpen(false);
        setShowPopup(true);
      }

      // Store chat metadata for header display
      if (res.data.chatMetadata) {
        setChatMetadata(res.data.chatMetadata);
      }
    } catch (error: any) {
      console.log("Error fetching message history:", error);
      if (error?.response?.data?.statusCode === 400) {
        toast.error(error?.response?.data?.message, {
          duration: Infinity,
        });
        setWrongChat(true);
      } else if (error?.response?.data?.statusCode === 500) {
        // Clear invalid tokens and refresh
        const { removeAuthToken } = await import("@/lib/tokenManager");
        await removeAuthToken();
        router.refresh();
      } else {
        toast.error("Something went wrong.", {});
      }
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  useEffect(() => {
    if (roomIdentifier) {
      getMessageHistory(roomIdentifier);
    }
  }, [roomIdentifier]);

  const uploadImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;
    try {
      const file = e.target.files[0];
      const validation = validateFile(file);

      if (!validation.isValid) {
        toast(validation.error);
        return;
      }

      setLoader(true);

      const data = new FormData();
      data.append("file", e.target.files[0]);
      if (user?.user_id && roomId) {
        data.append("user_id", user?.user_id.toString());
        data.append("room_id", roomId?.toString() ?? "");
      }

      const res = await apiClient({
        method: "POST",
        endpoint: "files/upload",
        data,
        isFormData: true,
      });

      socket?.emit("send-message", {
        sender_id: provider?.user_id,
        message: "ATTACHMENT",
        type: "room",
        file_path: res.fileUrl,
        file_id: res.fileId,
        room_id: roomId,
        sender: {
          user_id: user?.user_id,
          name: user?.first_name + " " + user?.last_name,
        },
      });

      const newMessage = [
        ...messageHistory,
        {
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.fileUrl,
          },
          sender: {
            user_id: user?.user_id,
            name: user?.first_name + " " + user?.last_name,
          },
        },
      ];

      setMessageHistory(newMessage);
      setLoader(false);
    } catch (error) {
      toast.error("Upload Failed");
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  const handledMessageSend = async (message: string) => {
    socket?.emit("send-message", {
      sender_id: user?.user_id,
      message: message,
      type: "room",
      room_id: roomId,
      sender: {
        user_id: user?.user_id,
        first_name: user?.first_name,
        last_name: user?.last_name,
        name: user?.first_name + " " + user?.last_name,
      },
    });

    const newMessage = [
      ...(messageHistory ?? []),
      {
        message: message,
        type: "room",
        roomId: roomId,
        sender: {
          user_id: user?.user_id,
          first_name: user?.first_name,
          last_name: user?.last_name,
          name: user?.first_name + " " + user?.last_name,
        },
        created_at: new Date().toISOString(),
      },
    ];
    setMessageHistory(newMessage);
  };

  const handleOnAttached = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*,.pdf,.doc,.docx";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        uploadImage({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  };

  useEffect(() => {
    socket?.on("receive-message", (data: any) => {
      const newMessage = [...(messageHistory ?? []), data];
      // Get sender name from the received message data
      const senderName =
        data.sender?.first_name && data.sender?.last_name
          ? `${data.sender.first_name} ${data.sender.last_name}`
          : data.sender?.name || "Unknown User";

      sendNotification({
        title: `New Message from ${senderName}`,
        body: data.message,
      });
      readAllMessage();
      setMessageHistory(newMessage);
    });

    socket?.on("toggle-room-status", (data: any) => {
      const { room_status } = data;
      if (room_status === "inactive") {
        console.log("Chat room closed by provider");
        setIsConsultOpen(false);
        setShowPopup(true);
      } else {
        setIsConsultOpen(true);
        setShowPopup(false);
      }
    });

    return () => {
      socket?.off("receive-message");
    };
  }, [socket, messageHistory]);

  useEffect(() => {
    if (patient?.user_id && roomId && socket) {
      toast.success("Joined chat room successfully");
      socket.emit("join", {
        room_id: roomId,
        user_id: patient?.user_id,
        name: "user",
      });
    }
  }, [socket, roomId, patient?.user_id]);

  const getFileSignUrl = async (file_id: string) => {
    setLoader(true);

    try {
      // Use cookie-based token management instead of localStorage
      const { getAuthToken } = await import("@/lib/tokenManager");
      const token = await getAuthToken();
      const res = await apiClient({
        method: "POST",
        endpoint: "files/sign-url",
        data: { file_id },
        token: token || undefined,
      });

      setImageModalVisible(true);
      setImageModalValue(res.data.url);
      setLoader(false);
    } catch (error) {
      setLoader(false);
    } finally {
      setLoader(false);
    }
  };

  return (
    <Suspense>
      <Loader show={loader} />
      <ChatContainer
        style={{
          height: "100vh",
        }}
      >
        <ConversationHeader>
          <Avatar
            name={"receiverFullName"}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content
            userName={receiverFullName}
            info={
              <div className="text-xs text-gray-500 space-y-1">
                {chatMetadata?.room?.description && (
                  <div>{chatMetadata.room.description}</div>
                )}
                {chatMetadata?.service?.display_service_name && (
                  <div className="font-medium text-blue-600">
                    Service: {chatMetadata.service.display_service_name}
                  </div>
                )}
              </div>
            }
          />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {chatMetadata?.participants && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setParticipantsPopupVisible(true)}
                title="View Participants"
              >
                <Users className="h-4 w-4" />
              </Button>
            )}

            {isConnected && <CheckCircle />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>
        <MessageList>
          {messageHistory &&
            messageHistory?.map((data, idx) => {
              const isSendedByMe = data.sender.user_id === currentUserId;
              const fullName =
                data.sender?.first_name && data.sender?.last_name
                  ? `${data.sender.first_name} ${data.sender.last_name}`
                  : data.sender?.name || "Unknown";
              const formattedTime = formatChatMessageDate(data.created_at);
              return (
                <Message
                  key={idx}
                  onClick={() => {
                    if (data.file?.path) {
                      getFileSignUrl(data.file?.path);
                    }
                  }}
                  model={{
                    direction: isSendedByMe ? "outgoing" : "incoming",
                    // message: data?.message?.replace(/<[^>]*>?/gm, ''),
                    position: "last",
                    sender: senderFullName,
                    sentTime: data?.created_at,
                    type: "html",
                  }}
                >
                  {data?.message === "ATTACHMENT" ? (
                    <Message.ImageContent
                      className="cursor-pointer"
                      src={`/images/${
                        isImageType(data.file?.path) ? "file.png" : "pdf.png"
                      }`}
                      height={100}
                    />
                  ) : (
                    <Message.HtmlContent
                      html={`<b>${fullName}: </b> <br/>${data?.message} <br/><em style='font-size:10px'>${formattedTime}</em>`}
                    />
                  )}
                  {/* {data?.message === "ATTACHMENT" ? <Message.ImageContent className='cursor-pointer' src={`/images/file.png`} height={100} /> : <Message.HtmlContent html={data?.message} />} */}
                  {isSendedByMe ? (
                    <></>
                  ) : (
                    <Avatar
                      name="Sumanta Dey"
                      src={`/images/${
                        role === "PROVIDER" ? "patient.png" : "provider.png"
                      }`}
                    />
                  )}
                </Message>
              );
            })}
        </MessageList>
        {isConsultOpen && (
          <MessageInput
            autoFocus
            placeholder="Type message here"
            onSend={handledMessageSend}
            onAttachClick={handleOnAttached}
          />
        )}
      </ChatContainer>

      <FileDisplayContainer
        src={imageModalValue}
        imageModalVisible={imageModalVisible}
        setImageModalVisible={setImageModalVisible}
      />
      {showPopup && (
        <ClosedSession open={showPopup} onOpenChange={setShowPopup} />
      )}

      <WrongChat open={wrongChat} />
      {/* Participants Popup */}
      {chatMetadata?.participants && (
        <ParticipantsPopup
          isOpen={participantsPopupVisible}
          onClose={() => setParticipantsPopupVisible(false)}
          participants={chatMetadata.participants}
        />
      )}
    </Suspense>
  );
}
