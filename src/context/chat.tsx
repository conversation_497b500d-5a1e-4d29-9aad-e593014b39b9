"use client";
import React, { createContext, useContext, useState, useCallback } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { apiClient } from "@/lib/api";
import { getAuthToken, removeAuthToken } from "@/lib/tokenManager";
import { SocketContext } from "@/context/socket";
import { validateFile } from "@/lib/utils";
import { ChatMetadata } from "@/types/chat";

interface User {
  user_id: string | number;
  first_name: string;
  last_name: string;
  email?: string;
  status?: string;
  user_guid?: string;
}

interface ChatMessage {
  message: string;
  type: string;
  room_id?: string;
  roomId?: string;
  sender: {
    user_id: string | number;
    first_name?: string;
    last_name?: string;
    name: string;
  };
  created_at: string;
  file?: {
    user_file_id?: string;
    path: string;
  };
  file_id?: string;
}

interface ChatContextType {
  // State
  messageHistory: ChatMessage[];
  loader: boolean;
  imageModalVisible: boolean;
  imageModalValue: string;
  chatMetadata: ChatMetadata | null;
  participantsPopupVisible: boolean;
  isConsultOpen: boolean;
  showPopup: boolean;
  wrongChat: boolean;

  // Actions
  setMessageHistory: (messages: ChatMessage[]) => void;
  setLoader: (loading: boolean) => void;
  setImageModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  setImageModalValue: (value: string) => void;
  setChatMetadata: (metadata: ChatMetadata | null) => void;
  setParticipantsPopupVisible: (visible: boolean) => void;
  setIsConsultOpen: (open: boolean) => void;
  setShowPopup: (show: boolean) => void;
  setWrongChat: (wrong: boolean) => void;

  // Methods
  getMessageHistory: (roomIdentifier: string, token?: string) => Promise<void>;
  readAllMessage: (roomIdentifier: string, token?: string) => Promise<void>;
  handledMessageSend: (
    message: string,
    roomId: string,
    currentUser: User,
  ) => void;
  uploadImage: (
    e: React.ChangeEvent<HTMLInputElement>,
    roomId: string,
    currentUser: User,
  ) => Promise<void>;
  getFileSignUrl: (file_id: string) => Promise<void>;
  toggleSwitch: (
    enabled: boolean,
    roomId: string,
    currentUserId: string | number,
    patientId: string | number,
    senderFullName: string,
    token?: string,
  ) => Promise<void>;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

export const useChatContext = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChatContext must be used within a ChatProvider");
  }
  return context;
};

interface ChatProviderProps {
  children: React.ReactNode;
}

export const ChatProvider: React.FC<ChatProviderProps> = ({ children }) => {
  // State
  const [messageHistory, setMessageHistory] = useState<ChatMessage[]>([]);
  const [loader, setLoader] = useState(false);
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [imageModalValue, setImageModalValue] = useState("");
  const [chatMetadata, setChatMetadata] = useState<ChatMetadata | null>(null);
  const [participantsPopupVisible, setParticipantsPopupVisible] =
    useState(false);
  const [isConsultOpen, setIsConsultOpen] = useState<boolean>(true);
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [wrongChat, setWrongChat] = useState(false);

  // Hooks
  const { socket } = useContext(SocketContext);
  const router = useRouter();

  // Methods
  const getMessageHistory = useCallback(
    async (roomIdentifier: string, token?: string) => {
      setLoader(true);
      try {
        const authToken = token || (await getAuthToken());
        const res = await apiClient({
          method: "GET",
          endpoint: `chat/${roomIdentifier}/messages`,
          data: {},
          token: authToken || undefined,
        });

        if (res.statusCode === 200) {
          toast.success("Chat room initiated.", {
            duration: 1000,
            closeButton: true,
          });
          setMessageHistory(res.data?.messages ?? []);

          // Store chat metadata for header display
          if (res.data?.chatMetadata) {
            setChatMetadata(res.data.chatMetadata);
          }

          // Handle room status for patient view
          if (res.data.chatMetadata?.room?.status === "active") {
            setIsConsultOpen(true);
            setShowPopup(false);
          } else {
            setIsConsultOpen(false);
            setShowPopup(true);
          }
        } else {
          toast(res.message, {
            closeButton: true,
            duration: 1000,
          });
        }
      } catch (error: any) {
        console.log("Error fetching message history:", error);
        if (error?.response?.data?.statusCode === 400) {
          toast.error(error?.response?.data?.message, {
            duration: Infinity,
          });
          setWrongChat(true);
        } else if (error?.response?.data?.statusCode === 500) {
          // Clear invalid tokens and refresh
          await removeAuthToken();
          router.refresh();
        } else {
          toast.error("Something went wrong.");
        }
      } finally {
        setLoader(false);
      }
    },
    [router],
  );

  const readAllMessage = useCallback(
    async (roomIdentifier: string, token?: string) => {
      try {
        const authToken = token || (await getAuthToken());
        await apiClient({
          method: "GET",
          endpoint: `chat/${roomIdentifier}/read-all-messages`,
          data: {},
          token: authToken || undefined,
        });
      } catch (error) {
        console.error("Error reading messages:", error);
      }
    },
    [],
  );

  const handledMessageSend = useCallback(
    (message: string, roomId: string, currentUser: User) => {
      const senderFullName = `${currentUser?.first_name} ${currentUser?.last_name}`;

      socket?.emit("send-message", {
        message: message,
        type: "room",
        room_id: roomId,
        sender: {
          user_id: currentUser?.user_id,
          first_name: currentUser?.first_name,
          last_name: currentUser?.last_name,
          name: senderFullName,
        },
      });

      const newMessage: ChatMessage = {
        message: message,
        type: "room",
        roomId: roomId,
        sender: {
          user_id: currentUser?.user_id,
          first_name: currentUser?.first_name,
          last_name: currentUser?.last_name,
          name: senderFullName,
        },
        created_at: new Date().toISOString(),
      };

      setMessageHistory((prev) => [...prev, newMessage]);
    },
    [socket],
  );

  const uploadImage = useCallback(
    async (
      e: React.ChangeEvent<HTMLInputElement>,
      roomId: string,
      currentUser: User,
    ) => {
      if (!e.target.files) return;

      const file = e.target.files[0];
      const validation = validateFile(file);

      if (!validation.isValid) {
        toast(validation.error);
        return;
      }

      setLoader(true);
      try {
        const data = new FormData();
        data.append("file", file);

        if (currentUser?.user_id && roomId) {
          data.append("user_id", currentUser.user_id.toString());
          data.append("room_id", roomId.toString());
        }

        const res = await apiClient({
          method: "POST",
          endpoint: "files/upload",
          data,
          isFormData: true,
        });

        const senderFullName = `${currentUser?.first_name} ${currentUser?.last_name}`;

        socket?.emit("send-message", {
          message: "ATTACHMENT",
          type: "room",
          file_path: res.fileUrl,
          file_id: res.fileId,
          room_id: roomId,
          sender: {
            user_id: currentUser?.user_id,
            name: senderFullName,
          },
        });

        const newMessage: ChatMessage = {
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.fileUrl,
          },
          sender: {
            user_id: currentUser?.user_id,
            name: senderFullName,
          },
          created_at: new Date().toISOString(),
        };

        setMessageHistory((prev) => [...prev, newMessage]);
      } catch (error) {
        toast.error("Upload Failed");
      } finally {
        setLoader(false);
      }
    },
    [socket],
  );

  const getFileSignUrl = useCallback(async (file_id: string) => {
    setLoader(true);
    try {
      const token = await getAuthToken();
      const res = await apiClient({
        method: "POST",
        endpoint: "files/sign-url",
        data: { file_id },
        token: token || undefined,
      });
      setImageModalVisible(true);
      setImageModalValue(res.data.url);
    } catch (error) {
      console.error("Error getting file sign URL:", error);
    } finally {
      setLoader(false);
    }
  }, []);

  const toggleSwitch = useCallback(
    async (
      enabled: boolean,
      roomId: string,
      currentUserId: string | number,
      patientId: string | number,
      senderFullName: string,
      token?: string,
    ) => {
      try {
        setLoader(true);
        const authToken = token || (await getAuthToken());
        await apiClient({
          method: "POST",
          endpoint: `room/${roomId}/${enabled ? "enable" : "disable"}`,
          data: {},
          token: authToken || undefined,
        });

        socket?.emit("toggle-room-status", {
          room_id: roomId,
          user_id: currentUserId,
          patient_id: patientId,
          name: senderFullName,
          room_status: enabled ? "active" : "inactive",
        });
      } catch (error) {
        toast.error("Something went wrong while toggling switch.");
      } finally {
        setLoader(false);
      }
    },
    [socket],
  );

  const value: ChatContextType = {
    // State
    messageHistory,
    loader,
    imageModalVisible,
    imageModalValue,
    chatMetadata,
    participantsPopupVisible,
    isConsultOpen,
    showPopup,
    wrongChat,

    // Actions
    setMessageHistory,
    setLoader,
    setImageModalVisible,
    setImageModalValue,
    setChatMetadata,
    setParticipantsPopupVisible,
    setIsConsultOpen,
    setShowPopup,
    setWrongChat,

    // Methods
    getMessageHistory,
    readAllMessage,
    handledMessageSend,
    uploadImage,
    getFileSignUrl,
    toggleSwitch,
  };

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>;
};

export default ChatProvider;
